import fs from 'fs';
import crypto from 'crypto';
const hobbies = ['kanshu', 'game', 'yundong', 'muscic', 'dianying', 'lvyou', 'meishi', 'sheying', 'huihua', 'xiezuo'];

// AI识别验证码
async function solveCaptcha(imageBase64) {
    try {
        const response = await fetch("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", {
            method: 'POST',
            headers: {
                "accept": "*/*",
                "accept-language": "zh-CN",
                "authorization": "Bearer sk-a7fd0c335ca94d6a9eb3056d2f7c2cf5",
                "content-type": "application/json",
                "priority": "u=1, i",
                "sec-ch-ua": "\"Not.A/Brand\";v=\"99\", \"Chromium\";v=\"136\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "cross-site"
            },
            body: JSON.stringify({
                "model": "qwen-vl-max",
                "stream": false,
                "temperature": 0.1,
                "top_p": 1,
                "max_tokens": 1024,
                "messages": [
                    {
                        "role": "system",
                        "content": ""
                    },
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "请识别图片中的验证码。这是一个包含字母和数字的验证码，通常是4-6位。请仔细识别每个字符，区分大小写，只返回验证码内容，不要任何其他文字、标点符号或解释。"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": `data:image/gif;base64,${imageBase64}`
                                }
                            }
                        ]
                    }
                ]
            })
        });

        if (!response.ok) {
            throw new Error(`AI识别失败: ${response.status}`);
        }

        const result = await response.json();
        const captchaText = result.choices[0].message.content.trim();
        console.log('AI识别验证码结果:', captchaText);
        return captchaText;
    } catch (error) {
        console.error('验证码识别出错:', error);
        return null;
    }
}

// 获取验证码图片和session
async function getCaptcha() {
    try {
        const timestamp = Date.now();
        const response = await fetch(`https://www.tline.website/auth/captcha?t=${timestamp}`, {
            method: 'GET',
            headers: {
                "accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "cache-control": "no-cache",
                "pragma": "no-cache",
                "priority": "i",
                "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "image",
                "sec-fetch-mode": "no-cors",
                "sec-fetch-site": "same-origin",
                "cookie": "lang=zh-cn",
                "Referer": "https://www.tline.website/auth/login?type=register"
            }
        });

        if (!response.ok) {
            throw new Error(`获取验证码失败: ${response.status}`);
        }

        // 获取PHPSESSID
        const setCookieHeader = response.headers.get('set-cookie');
        let phpSessionId = '';
        if (setCookieHeader) {
            const match = setCookieHeader.match(/PHPSESSID=([^;]+)/);
            if (match) {
                phpSessionId = match[1];
            }
        }

        // 将图片转换为base64
        const imageBuffer = await response.arrayBuffer();
        const imageBase64 = Buffer.from(imageBuffer).toString('base64');

        // 保存验证码图片用于调试
        const saveTimestamp = Date.now();
        if (!fs.existsSync('debug')) {
            fs.mkdirSync('debug');
        }
        fs.writeFileSync(`debug/captcha_${saveTimestamp}.gif`, Buffer.from(imageBuffer));

        // 使用AI识别验证码
        const captchaText = await solveCaptcha(imageBase64);

        console.log(`验证码图片已保存: debug/captcha_${saveTimestamp}.gif, 识别结果: ${captchaText}`);

        return {
            phpSessionId,
            captchaText,
            imageBase64
        };
    } catch (error) {
        console.error('获取验证码出错:', error);
        return null;
    }
}

// 生成随机账号
function generateAccount() {
    //将大写的L改成随机26个字母大写
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const randomLetter = letters.charAt(Math.floor(Math.random() * letters.length));
    return randomLetter + Math.floor(******** + Math.random() * ********);
}

// 生成随机密码
function generatePassword() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const length = Math.floor(Math.random() * 10) + 8;
    let password = '';
    for (let i = 0; i < length; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
}

// 获取随机爱好
function getRandomHobby() {
    return hobbies[Math.floor(Math.random() * hobbies.length)];
}

// MD5加密函数
function md5(text) {
    return crypto.createHash('md5').update(text).digest('hex');
}

// 将对象转换为排序后的查询字符串
function jsonToSortedQueryString(obj) {
    const keys = Object.keys(obj).sort();
    const params = new URLSearchParams();
    keys.forEach((key) => {
        if (key !== 'sign') {
            params.append(key, obj[key]);
        }
    });
    return params.toString();
}

// 生成签名
function sign(params, keyCode) {
    return md5(jsonToSortedQueryString(params) + keyCode);
}

// 主函数
async function main() {
    const keyCode = "MLE!^Re4XcsrxBbR&!DvenL$";
    const accounts = [];
    let successAccounts = [];

    // 生成5个账号用于测试
    for (let i = 0; i < 5; i++) {
        const name = generateAccount();
        const account = {
            name: name,
            email: name,
            passwd: generatePassword(),
            question: '您最大的爱好是?',
            answer: getRandomHobby(),
            code: "666",
            captchaCode: "",
            emailcode:""
        };
        accounts.push(account);
    }

    // 注册账号
    for (const account of accounts) {
        console.log('正在注册：', account.name);

        let retryCount = 0;
        const maxRetries = 3;
        let registered = false;

        while (retryCount < maxRetries && !registered) {
            // 获取验证码
            const captchaData = await getCaptcha();
            if (!captchaData || !captchaData.captchaText || !captchaData.phpSessionId) {
                console.log(`账号 ${account.name} 获取验证码失败，重试 ${retryCount + 1}/${maxRetries}`);
                retryCount++;
                await new Promise((resolve) => setTimeout(resolve, 3000));
                continue;
            }

            // 设置验证码
            account.captchaCode = captchaData.captchaText;

            const params = { ...account };
            params.sign = sign(params, keyCode);
            const body = new URLSearchParams(params).toString()

            console.log('注册参数：', body);
            console.log('验证码：', captchaData.captchaText);
            console.log('PHPSESSID：', captchaData.phpSessionId);

            try {
                const response = await fetch("https://www.tline.website/auth/register", {
                    method: 'POST',
                    headers: {
                        "accept": "application/json, text/javascript, */*; q=0.01",
                        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                        "cache-control": "no-cache",
                        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                        "pragma": "no-cache",
                        "priority": "u=1, i",
                        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
                        "sec-ch-ua-mobile": "?0",
                        "sec-ch-ua-platform": "\"Windows\"",
                        "sec-fetch-dest": "empty",
                        "sec-fetch-mode": "cors",
                        "sec-fetch-site": "same-origin",
                        "x-app": "WEB",
                        "x-requested-with": "XMLHttpRequest",
                        "cookie": `lang=zh-cn; PHPSESSID=${captchaData.phpSessionId}`,
                        "referer": "https://www.tline.website/auth/login?type=register"
                    },
                    body: body
                });

                if (response.status === 429) {
                    console.log(`账号 ${account.name} 请求过于频繁，等待30秒后重试 ${retryCount + 1}/${maxRetries}`);
                    retryCount++;
                    await new Promise((resolve) => setTimeout(resolve, 30000));
                    continue;
                }

                if (!response.ok) {
                    console.log(`账号 ${account.name} 注册失败: ${response.status},${response.statusText}，重试 ${retryCount + 1}/${maxRetries}`);
                    retryCount++;
                    await new Promise((resolve) => setTimeout(resolve, 5000));
                    continue;
                }

                const result = await response.json();
                if(result.ret != 1)
                {
                    if (result.msg && result.msg.includes('验证码')) {
                        console.log(`账号 ${account.name} 验证码错误，重新获取验证码 ${retryCount + 1}/${maxRetries}`);
                        retryCount++;
                        await new Promise((resolve) => setTimeout(resolve, 2000));
                        continue;
                    } else {
                        console.log(`账号 ${account.name} 注册失败,错误消息:${result.msg}`);
                        break;
                    }
                }

                successAccounts.push(account);
                console.log(`账号 ${account.name} 注册成功,${JSON.stringify(result)}`);
                registered = true;

            } catch (error) {
                console.error(`账号 ${account.name} 注册出错:`, error);
                retryCount++;
                await new Promise((resolve) => setTimeout(resolve, 5000));
            }
        }

        if (!registered) {
            console.log(`账号 ${account.name} 注册失败，已达到最大重试次数`);
        }

        // 每个账号之间等待5秒
        await new Promise((resolve) => setTimeout(resolve, 5000));
    }

    // 保存成功注册的账号
    if (successAccounts.length > 0) {
        const successCount = successAccounts.length;
        if (fs.existsSync('data/tline_accounts.json')) {
            const existingData = fs.readFileSync('data/tline_accounts.json', 'utf8');
            if (existingData.trim() !== '') {
                const existingAccounts = JSON.parse(existingData);
                successAccounts = existingAccounts.concat(successAccounts);
            }
        }
        fs.writeFileSync('data/tline_accounts.json', JSON.stringify(successAccounts, null, 2));
        console.log(`成功注册 ${successCount} 个账号，已保存到 tline_accounts.json`);
    }
}

main().catch(console.error);
